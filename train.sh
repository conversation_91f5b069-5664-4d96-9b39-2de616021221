#!/bin/bash
# 训练脚本 - 支持困难负样本损失函数
# dataset: CIRR或FashionIQ
# noise_ratio: 噪声比例
# gpu: 指定要使用的GPU设备编号（0, 1, 2...）
# use_hard_neg: 是否启用困难负样本损失

your_exp_name=lrm_with_hard_neg-test_8-23_hard_RCL_10
shuffle_seed=42
seed=42
dataset=CIRR
# 指定使用的GPU编号，注意这里直接使用数字，不要加引号
gpu=1

noise_ratio=0.0

# 困难负样本损失参数
use_hard_neg=true  # 设置为false禁用困难负样本损失
hard_neg_loss="hard_RCL"  # 可选: infoNCE, triplet, RCL, hard_RCL, weighted_RCL
hard_neg_top_k=10
hard_neg_weight=0.3
hard_neg_margin=0.2

echo "指定使用GPU: ${gpu}"
echo "困难负样本损失: ${use_hard_neg}"

# 构建基础命令
cmd="python src/precompute_train.py \
    --exp_name \"${your_exp_name}\" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 30 \
    --lr \"1e-5\" \
    --positive_loss \"RCL\" \
    --trade_off 1.0 \
    --save_training \
    --gpu ${gpu}"

# 如果启用困难负样本损失，添加相关参数
if [ "$use_hard_neg" = "true" ]; then
    cmd="$cmd \
    --use_hard_neg \
    --hard_neg_loss \"${hard_neg_loss}\" \
    --hard_neg_top_k ${hard_neg_top_k} \
    --hard_neg_weight ${hard_neg_weight} \
    --hard_neg_margin ${hard_neg_margin}"
    echo "困难负样本损失参数: loss=${hard_neg_loss}, top_k=${hard_neg_top_k}, weight=${hard_neg_weight}, margin=${hard_neg_margin}"
fi

# 执行命令
eval $cmd